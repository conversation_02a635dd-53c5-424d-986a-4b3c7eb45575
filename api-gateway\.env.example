# API Gateway Configuration
PORT=3000
NODE_ENV=development

# Service URLs
AUTH_SERVICE_URL=http://localhost:3001
ARCHIVE_SERVICE_URL=http://localhost:3002
ASSESSMENT_SERVICE_URL=http://localhost:3003
NOTIFICATION_SERVICE_URL=http://localhost:3005

# Security
JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# Rate Limiting - Updated for high-volume testing (1000+ concurrent users)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=5000

# Rate Limiting Details:
# - General: 5000 requests per 15 minutes
# - Auth: 2500 requests per 15 minutes (register + login + profile)
# - Assessment: 1000 submissions per hour
# - Admin: 1000 requests per 15 minutes

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8080,http://localhost:5173

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined

# Health Check
HEALTH_CHECK_INTERVAL=30000
SERVICE_TIMEOUT=5000
